#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
售后绩效数据同步脚本
读取本地Excel中的售后部门绩效数据，并将其写入钉钉在线表格。
"""

import pandas as pd
from datetime import datetime, timedelta
import json
import os
import urllib3
import argparse
import sys
from dingtalk_sheet_utils import DingTalkSheetUtils

# 禁用InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def parse_arguments():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的参数对象
    """
    parser = argparse.ArgumentParser(
        description='售后绩效数据同步程序 - 从Excel读取数据并同步到钉钉表格',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python 售后绩效数据同步.py                    # 处理昨天的数据（默认）
  python 售后绩效数据同步.py --date 2025-07-20  # 处理指定日期的数据
  python 售后绩效数据同步.py -d 2025-07-20      # 使用短参数名
        '''
    )

    parser.add_argument(
        '--date', '-d',
        type=str,
        help='指定要处理的日期，格式：YYYY-MM-DD（如：2025-07-20）。不指定则默认处理昨天的数据。'
    )

    return parser.parse_args()

def validate_and_parse_date(date_str):
    """
    验证并解析日期字符串

    Args:
        date_str (str): 日期字符串，格式：YYYY-MM-DD

    Returns:
        datetime.date: 解析后的日期对象，如果格式错误则返回None
    """
    if not date_str:
        return None

    try:
        # 尝试解析日期
        parsed_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 检查日期是否在合理范围内
        today = datetime.now().date()
        min_date = today - timedelta(days=365)  # 最早一年前
        max_date = today + timedelta(days=30)   # 最晚一个月后

        if parsed_date < min_date:
            print(f"⚠️ 警告：指定日期 {date_str} 距离今天超过一年，请确认是否正确")
        elif parsed_date > max_date:
            print(f"⚠️ 警告：指定日期 {date_str} 是未来日期，请确认是否正确")

        return parsed_date

    except ValueError:
        print(f"❌ 日期格式错误：{date_str}")
        print(f"📋 正确格式：YYYY-MM-DD（如：2025-07-20）")
        return None

def load_config(config_path='config.json'):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 配置文件 {config_path} 未找到，请检查路径是否正确。")
        return None
    except json.JSONDecodeError:
        print(f"❌ 配置文件 {config_path} 格式错误，请检查JSON语法。")
        return None

def get_performance_data(file_path, sheet_name):
    """
    从Excel文件中读取指定工作表的绩效数据。

    Args:
        file_path (str): Excel文件路径。
        sheet_name (str): 工作表名称。

    Returns:
        pd.DataFrame or None: 如果成功则返回数据帧，否则返回None。
    """
    try:
        print(f"🔍 正在读取Excel文件: {file_path}")
        print(f"📄 工作表名称: {sheet_name}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ Excel文件不存在: {file_path}")
            return None

        # 读取Excel文件，并将所有列都作为字符串读取以避免格式问题
        df = pd.read_excel(file_path, sheet_name=sheet_name, dtype=str)
        print(f"✅ 成功读取Excel文件，共 {len(df)} 行数据")

        # 将'日期'列转换为datetime对象，以便进行日期比较
        if '日期' in df.columns:
            # 尝试多种日期格式进行转换
            df['日期'] = pd.to_datetime(df['日期'], errors='coerce', format='mixed')
            print(f"✅ 日期列转换完成")

            # 显示转换后的日期信息用于调试
            valid_dates = df[df['日期'].notna()]
            if not valid_dates.empty:
                print(f"📅 Excel中的日期数据:")
                for i, row in valid_dates.iterrows():
                    date_str = row['日期'].strftime('%Y-%m-%d')
                    print(f"  第{i+1}行: {date_str}")
            else:
                print(f"⚠️ 没有找到有效的日期数据")
        else:
            print(f"❌ 未找到'日期'列，可用列: {list(df.columns)}")
            return None

        return df
    except FileNotFoundError:
        print(f"❌ Excel文件未找到: {file_path}")
        return None
    except ValueError as e:
        if 'Worksheet named' in str(e) and sheet_name in str(e):
             print(f"❌ 在Excel文件中找不到名为 '{sheet_name}' 的工作表。")
        else:
            print(f"❌ 读取Excel时发生未知错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 读取Excel时发生意外错误: {e}")
        return None


def find_target_date_performance(df, target_date=None):
    """
    在数据帧中查找指定日期的绩效数据。

    Args:
        df (pd.DataFrame): 包含绩效数据的DataFrame。
        target_date (datetime.date, optional): 要查找的目标日期。如果为None，则查找昨天的数据。

    Returns:
        tuple: (pd.Series or None, datetime.date) - 找到的数据行和实际查找的日期
    """
    # 1. 确定要查找的日期
    if target_date is None:
        # 如果没有指定日期，使用昨天的日期
        current_time = datetime.now()
        target_date = (current_time - timedelta(days=1)).date()
        print(f"🕐 当前系统时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📅 默认处理昨天日期: {target_date}")
    else:
        print(f"📅 手动指定处理日期: {target_date}")

    # 2. 在Excel文件中查找"日期"列等于目标日期的数据行
    print(f"🔍 正在Excel中查找日期为 {target_date} 的数据...")

    # 确保日期列存在且有效
    valid_date_rows = df[df['日期'].notna()]
    if valid_date_rows.empty:
        print(f"❌ Excel中没有有效的日期数据")
        return None, target_date

    # 使用日期对象进行精确比较，避免格式差异
    matching_rows = []
    for index, row in valid_date_rows.iterrows():
        excel_date = row['日期'].date()  # 转换为日期对象进行比较
        if excel_date == target_date:
            matching_rows.append(row)
            print(f"✅ 找到匹配行: 第{index+1}行，日期={excel_date}")

    # 3. 如果找到匹配的数据行，读取该行的所有数据
    if matching_rows:
        print(f"✅ 共找到 {len(matching_rows)} 条目标日期的数据")
        # 返回第一条匹配的数据
        return matching_rows[0], target_date
    else:
        # 4. 如果没有找到匹配目标日期的数据行
        print(f"❌ 未找到日期为 {target_date} 的绩效数据")
        print(f"📋 Excel中现有的日期数据:")
        for index, row in valid_date_rows.iterrows():
            excel_date = row['日期'].date()
            print(f"  第{index+1}行: {excel_date}")
        return None, target_date

def main():
    """主执行函数"""
    print("🚀 开始执行售后绩效数据同步任务...")

    # 0. 解析命令行参数
    args = parse_arguments()

    # 验证日期参数
    target_date = None
    if args.date:
        target_date = validate_and_parse_date(args.date)
        if target_date is None:
            print("❌ 日期参数验证失败，程序退出")
            sys.exit(1)

    # 1. 加载配置
    config = load_config()
    if not config:
        return
        
    # 2. 初始化钉钉表格工具
    try:
        dingtalk_util = DingTalkSheetUtils(config)
    except Exception as e:
        print(f"❌ 初始化钉钉工具失败: {e}")
        return
        
    # 从配置中获取售后部门的相关信息
    after_sales_config = config.get('after_sales_checker', {})
    file_path = after_sales_config.get('file_path')
    sheet_name = after_sales_config.get('sheet_name')

    # 添加调试信息
    print(f"🔍 配置文件读取调试信息:")
    print(f"  当前工作目录: {os.getcwd()}")
    print(f"  配置文件中的Excel路径: {file_path}")
    print(f"  配置文件中的工作表名: {sheet_name}")
    print(f"  after_sales_config内容: {after_sales_config}")

    # 直接从钉钉配置中获取目标工作表名称
    target_sheet_name = dingtalk_util.sheet_name

    if not all([file_path, sheet_name, target_sheet_name]):
        print("❌ 配置文件中缺少 after_sales_checker 的必要信息（file_path, sheet_name）或钉钉表格配置不完整。")
        return

    # 3. 读取本地Excel数据
    performance_df = get_performance_data(file_path, sheet_name)
    if performance_df is None:
        return

    # 4. 查找目标日期的绩效数据
    target_data, actual_date = find_target_date_performance(performance_df, target_date)

    # 5. 准备要写入钉钉表格的数据
    # 根据Excel列名，我们需要 '售后客服回复超时人数', '订单审核', '物流同步失败订单'

    # 格式化日期用于写入钉钉表格
    date_str = actual_date.strftime('%Y/%#m/%#d')

    if target_data is None:
        # 如果没有找到目标日期的数据，写入"暂无数据"
        print(f"⚠️ 未找到日期 {actual_date} 的绩效数据，将写入'暂无数据'")
        row_to_write = [[
            date_str,
            "暂无数据",
            "暂无数据",
            "暂无数据"
        ]]
    else:
        # 如果找到了目标日期的数据，正常处理
        print(f"✅ 成功获取到日期 {actual_date} 的绩效数据:")
        print(target_data)

        try:
            # 使用直接索引访问数据
            timeout_count = target_data['售后客服回复超时人数']
            order_audit = target_data['订单审核']
            sync_failed_order = target_data['物流同步失败订单']

            # 准备写入钉钉表格的数据行, 将pandas的NaN值转换为空字符串
            row_to_write = [[
                date_str,
                "" if pd.isna(timeout_count) else str(timeout_count),
                "" if pd.isna(order_audit) else str(order_audit),
                "" if pd.isna(sync_failed_order) else str(sync_failed_order)
            ]]

        except KeyError as e:
            print(f"❌ Excel文件中缺少必要的列: {e}。将写入'暂无数据'")
            row_to_write = [[
                date_str,
                "暂无数据",
                "暂无数据",
                "暂无数据"
            ]]
        except Exception as e:
            print(f"❌ 处理数据时发生未知错误: {e}。将写入'暂无数据'")
            row_to_write = [[
                date_str,
                "暂无数据",
                "暂无数据",
                "暂无数据"
            ]]

    print(f"📋 准备写入钉钉的数据: {row_to_write}")

    # 6. 写入数据到钉钉表格
    handle_monthly_summary(dingtalk_util, target_sheet_name, row_to_write)
        
    print("🏁 任务执行结束。")

def handle_monthly_summary(dingtalk_util, sheet_name, data_row):
    """
    处理按月汇总的数据写入逻辑。

    Args:
        dingtalk_util (DingTalkSheetUtils): 钉钉表格工具实例。
        sheet_name (str): 工作表名称。
        data_row (list): 要写入的单行数据。
    """
    target_sheet_id = dingtalk_util._get_sheet_id_by_name(sheet_name)
    if not target_sheet_id:
        print(f"❌ 在钉钉表格中找不到名为 '{sheet_name}' 的工作表。")
        return

    current_month_title = datetime.now().strftime('%Y年%m月数据汇总')
    
    # 查找当前月份标题的位置
    title_location = find_title_row(dingtalk_util, target_sheet_id, current_month_title)

    if title_location:
        # 如果找到了当月标题，就在该标题下追加数据
        print(f"ℹ️ 找到当月标题 '{current_month_title}'，将在其下方追加数据。")
        append_data_under_title(dingtalk_util, target_sheet_id, title_location, data_row)
    else:
        # 如果没找到，则在表格末尾创建新的月度汇总区域
        print(f"ℹ️ 未找到当月标题，将在表格末尾创建新的月度汇总。")
        create_new_month_section(dingtalk_util, target_sheet_id, current_month_title, data_row)

def find_title_row(dingtalk_util, sheet_id, title, max_rows=500):
    """在工作表中查找标题所在的行号。"""
    print(f"🔍 正在查找标题: '{title}'")
    # 为了简单起见，我们只检查A列
    range_to_check = f"A1:A{max_rows}"
    try:
        content = dingtalk_util.get_cell_range(sheet_id, range_to_check)
        if content:
            for i, row in enumerate(content):
                if row and row[0] == title:
                    found_row = i + 1
                    print(f"✅ 找到标题在第 {found_row} 行。")
                    return found_row
    except Exception as e:
        print(f"❌ 查找标题时出错: {e}")
    return None

def append_data_under_title(dingtalk_util, sheet_id, title_row, data_row):
    """在指定标题下方智能追加或更新数据。"""
    # 从标题行下一行开始查找数据区域
    start_search_row = title_row + 2  # 数据开始行（跳过标题和表头）

    # 获取要写入的日期（从data_row中提取）
    target_date = data_row[0][0]  # data_row是二维数组，取第一行第一列的日期
    print(f"🔍 检查钉钉表格中是否已存在日期 {target_date} 的数据...")

    # 检查现有数据，查找是否已存在该日期的记录
    range_to_check = f"A{start_search_row}:D{start_search_row + 200}"  # 假设一个月不超过200条记录
    try:
        content = dingtalk_util.get_cell_range(sheet_id, range_to_check)
        existing_date_row = None
        last_data_row = start_search_row - 1
        insert_position = None

        if content:
            for i, row_content in enumerate(content):
                current_row = start_search_row + i

                # 如果这一行为空，说明数据结束了
                if not row_content or not row_content[0]:
                    break

                last_data_row = current_row
                existing_date = row_content[0] if row_content else ""

                # 检查是否已存在相同日期的数据
                if existing_date == target_date:
                    existing_date_row = current_row
                    print(f"✅ 找到已存在的日期 {target_date}，位于第 {current_row} 行")
                    break

                # 寻找合适的插入位置（按日期顺序）
                if insert_position is None:
                    try:
                        # 尝试解析现有日期进行比较
                        from datetime import datetime
                        existing_dt = datetime.strptime(existing_date, '%Y/%m/%d')
                        target_dt = datetime.strptime(target_date, '%Y/%m/%d')

                        # 如果目标日期应该插入在当前行之前
                        if target_dt < existing_dt:
                            insert_position = current_row
                    except:
                        # 如果日期解析失败，继续查找
                        pass

        # 根据检查结果决定操作
        if existing_date_row:
            # 更新已存在的行
            print(f"📝 更新第 {existing_date_row} 行的数据")
            write_range = f"A{existing_date_row}:D{existing_date_row}"
            dingtalk_util.write_cell_range(sheet_id, write_range, data_row)
        elif insert_position:
            # 在指定位置插入新行（需要先移动后面的数据）
            print(f"📥 在第 {insert_position} 行插入新数据（按日期顺序）")
            # 注意：钉钉表格API不支持插入行，所以我们在最后追加
            next_row = last_data_row + 1
            write_range = f"A{next_row}:D{next_row}"
            dingtalk_util.write_cell_range(sheet_id, write_range, data_row)
        else:
            # 在末尾追加新数据
            next_row = last_data_row + 1
            print(f"📈 在第 {next_row} 行追加新数据")
            write_range = f"A{next_row}:D{next_row}"
            dingtalk_util.write_cell_range(sheet_id, write_range, data_row)

    except Exception as e:
        print(f"❌ 处理数据时出错: {e}")


def create_new_month_section(dingtalk_util, sheet_id, title, data_row):
    """在表格末尾创建新的月度汇总区域。"""
    last_row = dingtalk_util.find_last_data_row(sheet_id)
    # 如果工作表为空，则从第1行开始；否则，在最后一行数据下方空一行再开始。
    start_new_section_row = 1 if last_row == 0 else last_row + 2
    
    print(f"📄 将在第 {start_new_section_row} 行创建新的月度汇总区域。")
    
    # 写入月份标题
    title_range = f"A{start_new_section_row}"
    dingtalk_util.write_cell_range(sheet_id, title_range, [[title]])
    dingtalk_util.merge_cells(sheet_id, f"A{start_new_section_row}:D{start_new_section_row}")
    dingtalk_util.set_cell_alignment(sheet_id, f"A{start_new_section_row}", alignment='center')

    # 写入表头
    header_row = start_new_section_row + 1
    headers = [['日期', '售后客服回复超时人数', '订单审核', '物流同步失败订单']]
    header_range = f"A{header_row}:D{header_row}"
    dingtalk_util.write_cell_range(sheet_id, header_range, headers)
    
    # 写入第一行数据
    data_start_row = header_row + 1
    data_range = f"A{data_start_row}:D{data_start_row}"
    dingtalk_util.write_cell_range(sheet_id, data_range, data_row)


if __name__ == '__main__':
    # 切换工作目录到脚本所在目录，以确保相对路径正确
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    main() 