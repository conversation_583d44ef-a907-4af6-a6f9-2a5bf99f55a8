#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
钉钉在线表格工具类
用于向钉钉在线表格写入数据
"""

import requests
import json
from datetime import datetime
from typing import List, Dict, Any
import urllib3


class DingTalkSheetUtils:
    def __init__(self, config: Dict):
        """
        初始化钉钉表格工具类

        Args:
            config: 完整的配置字典
        """
        # 从配置中获取基础信息
        sheet_config = config.get('dingtalk', {}).get('sheet', {})
        self.app_key = sheet_config.get('app_key')
        self.app_secret = sheet_config.get('app_secret')
        self.operator_id = sheet_config.get('operator_id')

        # 根据当前环境选择表格配置
        current_env = config.get('dingtalk', {}).get('current_environment', 'prod')
        if current_env == 'test':
            env_sheet_config = sheet_config.get('test_sheet', {})
            self.env_name = "测试环境"
        else:
            env_sheet_config = sheet_config.get('prod_sheet', {})
            self.env_name = "正式环境"

        self.sheet_id = env_sheet_config.get('sheet_id')
        self.sheet_name = env_sheet_config.get('sheet_name', '售后部绩效明细')
        self.sheet2_name = env_sheet_config.get('sheet2_name', '仓库绩效汇总')
        self.last_row = env_sheet_config.get('last_row', 1)

        # 保存完整配置用于更新last_row
        self.config = config
        self.current_env = current_env

        self.access_token = None
        self.primary_domain = "https://api.dingtalk.com/v1.0/doc/workbooks/"

        print(f"🔧 钉钉表格工具初始化 - {self.env_name}")
        print(f"📋 表格ID: {self.sheet_id}")
        print(f"📄 工作表1: {self.sheet_name}")
        print(f"📄 工作表2: {self.sheet2_name}")

        # 获取access_token
        self._get_access_token()

        # 设置基础请求头（简化配置，避免网络问题）
        self.default_headers = {
            'Content-Type': 'application/json',
            'x-acs-dingtalk-access-token': self.access_token
        }
        
        # 关闭urllib3的警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        print(f"✅ 钉钉表格工具初始化完成，表格ID: {self.sheet_id}")
    
    def _get_access_token(self):
        """获取钉钉访问令牌"""
        try:
            url = f"https://oapi.dingtalk.com/gettoken?appkey={self.app_key}&appsecret={self.app_secret}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            result = response.json()
            
            if 'access_token' in result:
                self.access_token = result['access_token']
                print("✅ 获取钉钉访问令牌成功")
            else:
                raise Exception(f"获取访问令牌失败: {result}")
                
        except Exception as e:
            print(f"❌ 获取钉钉访问令牌失败: {str(e)}")
            raise

    def update_last_row(self, new_last_row: int):
        """
        更新配置文件中的last_row

        Args:
            new_last_row: 新的最后一行行号
        """
        try:
            # 根据当前环境更新对应的last_row
            if self.current_env == 'test':
                self.config['dingtalk']['sheet']['test_sheet']['last_row'] = new_last_row
            else:
                self.config['dingtalk']['sheet']['prod_sheet']['last_row'] = new_last_row

            # 保存配置文件
            import json
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            print(f"📝 已更新{self.env_name}配置文件，记录最后一行: {new_last_row}")

        except Exception as e:
            print(f"❌ 更新配置文件失败: {str(e)}")
    
    def get_sheets_list(self) -> List[Dict]:
        """
        获取工作表列表

        Returns:
            工作表列表
        """
        try:
            url = f"{self.primary_domain}{self.sheet_id}/sheets?operatorId={self.operator_id}"

            response = requests.get(url, headers=self.default_headers, verify=False, timeout=10)
            response.raise_for_status()
            result = response.json()

            return result.get("value", [])

        except Exception as e:
            print(f"❌ 获取工作表列表失败: {str(e)}")
            return []

    def update_sheet_name(self, sheet_id: str, new_name: str) -> bool:
        """
        修改工作表名称

        Args:
            sheet_id: 工作表ID
            new_name: 新的工作表名称

        Returns:
            是否成功
        """
        try:
            url = f"{self.primary_domain}{self.sheet_id}/sheets/{sheet_id}?operatorId={self.operator_id}"

            data = {
                "name": new_name
            }

            response = requests.put(url, headers=self.default_headers, json=data, verify=False, timeout=10)

            if response.status_code == 200:
                print(f"✅ 成功修改工作表名称: {sheet_id} -> {new_name}")
                return True
            else:
                print(f"❌ 修改工作表名称失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 修改工作表名称异常: {str(e)}")
            return False

    def get_cell_range(self, sheet_id: str, cell_range: str) -> List[List[str]]:
        """
        获取单元格区域内容

        Args:
            sheet_id: 工作表ID（不是名称）
            cell_range: 单元格范围，如 "A1:C10"

        Returns:
            单元格内容的二维列表
        """
        try:
            url = (f"{self.primary_domain}{self.sheet_id}"
                   f"/sheets/{sheet_id}/ranges/{cell_range}"
                   f"?operatorId={self.operator_id}")

            response = requests.get(url, headers=self.default_headers, verify=False, timeout=30)
            response.raise_for_status()
            result = response.json()

            return result.get("displayValues", [])

        except Exception as e:
            print(f"❌ 获取单元格内容失败: {str(e)}")
            return []
    
    def write_cell_range(self, sheet_id: str, cell_range: str, values: List[List[Any]]) -> bool:
        """
        写入单元格区域内容

        Args:
            sheet_id: 工作表ID
            cell_range: 单元格范围，如 "A1:C10"
            values: 要写入的数据，二维列表

        Returns:
            是否成功
        """
        try:
            url = (f"{self.primary_domain}{self.sheet_id}"
                   f"/sheets/{sheet_id}/ranges/{cell_range}"
                   f"?operatorId={self.operator_id}")

            print(f"🔍 调试信息:")
            print(f"  URL: {url}")
            print(f"  数据: {values}")

            data = {"values": values}
            response = requests.put(url, data=json.dumps(data), headers=self.default_headers, verify=False, timeout=30)

            print(f"  响应状态码: {response.status_code}")
            print(f"  响应内容: {response.text}")

            if response.status_code == 200:
                print(f"✅ 成功写入数据到工作表 {sheet_id}!{cell_range}")
                return True
            else:
                print(f"❌ 写入数据失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 写入单元格内容失败: {str(e)}")
            return False
    
    def clear_cell_range(self, sheet_name: str, cell_range: str) -> bool:
        """
        清空单元格区域
        
        Args:
            sheet_name: 工作表名称
            cell_range: 单元格范围，如 "A1:C10"
            
        Returns:
            是否成功
        """
        try:
            url = (f"{self.primary_domain}{self.sheet_id}"
                   f"/sheets/{sheet_name}/ranges/{cell_range}/clear"
                   f"?operatorId={self.operator_id}")
            
            response = requests.post(url, headers=self.default_headers, verify=False, timeout=30)
            
            if response.status_code == 200:
                print(f"✅ 成功清空 {sheet_name}!{cell_range}")
                return True
            else:
                print(f"❌ 清空数据失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 清空单元格失败: {str(e)}")
            return False
    

    


    def append_warehouse_report_block(self, sheet_id: str, status_data: Dict, shipping_data: Dict, reward_data: Dict = None, last_row_hint: int = None) -> tuple:
        """
        追加一个完整的仓库报告块到表格（每次运行添加一个报告块）

        报告块格式：
        - 时间戳
        - 仓库状态数据（待补货、待处理、生产单未完成、已审核）
        - 发货数据
        - 空行分隔

        Args:
            sheet_id: 工作表ID
            status_data: 仓库状态数据
            shipping_data: 发货数据

        Returns:
            (是否成功, 结束行号)
        """
        try:
            # 获取下一个可用的位置
            next_row = self._get_next_available_row(sheet_id, last_row_hint)
            if next_row is None:
                return False, None

            # 如果不是第一次写入（next_row > 1），处理分隔空行的合并
            if next_row > 1:
                separator_row = next_row - 1  # 分隔空行
                separator_range = f"A{separator_row}:C{separator_row}"
                print(f"🔗 正在合并分隔空行: {separator_range}")
                self._merge_cells(sheet_id, separator_range, "")

            # 准备当前时间
            now = datetime.now()
            current_time = now.strftime('%Y/%m/%d %H:%M:%S')

            # 构建报告数据块
            report_data = []

            # 添加时间戳（只有一行，时间先留空，后面通过合并单元格写入）
            report_data.append(['数据生成时间', '', ''])

            # 添加仓库状态数据
            report_data.append(['仓库状态数据', '', ''])
            report_data.append(['类型', '品数', '订单数'])

            # 添加各种状态数据（数据通过合并单元格方法填入）
            waiting_stock = status_data.get('待补货', {})
            waiting_process = status_data.get('待处理', {})

            report_data.append(['待补货', str(waiting_stock.get('品数', 0)), str(waiting_stock.get('订单数', 0))])
            report_data.append(['待处理', str(waiting_process.get('品数', 0)), str(waiting_process.get('订单数', 0))])
            report_data.append(['生产单完成进度', '', ''])
            report_data.append(['已审核-定制', '', ''])
            report_data.append(['已审核-现货', '', ''])

            # 添加发货数据
            report_data.append(['发货数据', '', ''])
            report_data.append(['现货订单数', '', ''])
            report_data.append(['现货订单发货总重量', '', ''])
            report_data.append(['定制订单数', '', ''])
            report_data.append(['定制订单发货总重量', '', ''])

            # 添加奖惩数据（如果提供了奖惩数据）
            if reward_data:
                report_data.append(['奖惩计算', '', ''])
                report_data.append(['待补货奖惩', '', ''])
                report_data.append(['待处理奖惩', '', ''])
                report_data.append(['总计奖惩', '', ''])
                report_data.append(['奖惩计算结果', '', ''])  # 新增奖惩结果详情行

            # 计算写入范围
            end_row = next_row + len(report_data) - 1
            cell_range = f"A{next_row}:C{end_row}"

            # 写入报告数据块
            success = self.write_cell_range(sheet_id, cell_range, report_data)

            if success:
                # 先设置所有单元格居中对齐和基本格式
                self._set_comprehensive_format_for_report_block(sheet_id, next_row, end_row)
                # 然后合并单元格并写入数据（包括时间）
                self._merge_cells_for_report_block(sheet_id, next_row, end_row, status_data, shipping_data, current_time, reward_data)
                # 添加边框
                self._add_borders_to_report_block(sheet_id, next_row, end_row)
                print(f"✅ 成功追加仓库报告块到第 {next_row}-{end_row} 行")
                return True, end_row
            else:
                print(f"❌ 追加仓库报告块失败")
                return False, None

        except Exception as e:
            print(f"❌ 追加仓库数据失败: {str(e)}")
            return False, None

    def write_warehouse_report_data(self, sheet_name: str, status_data: Dict, shipping_data: Dict, pending_orders: List[str] = None) -> bool:
        """
        写入仓库报告数据到钉钉表格（兼容测试接口）

        Args:
            sheet_name: 工作表名称
            status_data: 仓库状态数据
            shipping_data: 发货数据
            pending_orders: 待处理订单列表（可选）

        Returns:
            是否成功
        """
        try:
            # 获取工作表ID
            sheet_id = self._get_sheet_id_by_name(sheet_name)
            if not sheet_id:
                print(f"❌ 找不到工作表: {sheet_name}")
                return False

            # 调用追加报告块方法
            return self.append_warehouse_report_block(sheet_id, status_data, shipping_data)

        except Exception as e:
            print(f"❌ 写入仓库报告数据失败: {str(e)}")
            return False

    def _get_sheet_id_by_name(self, sheet_name: str) -> str:
        """
        根据工作表名称获取工作表ID

        Args:
            sheet_name: 工作表名称

        Returns:
            工作表ID，如果找不到则返回工作表名称本身
        """
        # 对于钉钉表格，通常工作表名称就是ID
        # 如果需要更复杂的映射，可以在这里实现
        return sheet_name

    def _format_report_block(self, sheet_id: str, start_row: int, end_row: int) -> bool:
        """
        设置报告块的格式（居中、字体、加粗等）

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功
        """
        try:
            # 设置整个区域的基本格式：居中、黑体-简 10号字体
            base_format_data = {
                "values": [],  # 不修改值，只修改格式
                "backgroundColors": [],  # 不修改背景色
                "fontFamilies": [["黑体-简"] * 3] * (end_row - start_row + 1),  # 字体
                "fontSizes": [[10] * 3] * (end_row - start_row + 1),  # 字体大小
                "horizontalAlignments": [["center"] * 3] * (end_row - start_row + 1),  # 水平居中
                "verticalAlignments": [["middle"] * 3] * (end_row - start_row + 1),  # 垂直居中
            }

            # 设置整个区域的基本格式
            success1 = self._apply_cell_format(sheet_id, f"A{start_row}:C{end_row}", base_format_data)

            # 设置单个单元格加粗 - 尝试不同的参数名称
            single_bold_format_data = {
                "values": [],  # 不修改值，只修改格式
                "background_colors": [],  # 不修改背景色
                "font_families": [["黑体-简"]],  # 字体
                "font_sizes": [[10]],  # 字体大小
                "font_bolds": [[True]],  # 加粗
                "horizontal_alignments": [["center"]],  # 水平居中
                "vertical_alignments": [["middle"]],  # 垂直居中
            }

            # 设置3列加粗
            three_col_bold_format_data = {
                "values": [],  # 不修改值，只修改格式
                "background_colors": [],  # 不修改背景色
                "font_families": [["黑体-简"] * 3],  # 字体
                "font_sizes": [[10] * 3],  # 字体大小
                "font_bolds": [[True] * 3],  # 加粗
                "horizontal_alignments": [["center"] * 3],  # 水平居中
                "vertical_alignments": [["middle"] * 3],  # 垂直居中
            }

            # 设置所有标题字段加粗
            # 数据生成时间
            self._apply_cell_format(sheet_id, f"A{start_row}:A{start_row}", single_bold_format_data)
            # 仓库状态数据
            self._apply_cell_format(sheet_id, f"A{start_row + 1}:A{start_row + 1}", single_bold_format_data)
            # 类型、品数、订单数
            self._apply_cell_format(sheet_id, f"A{start_row + 2}:C{start_row + 2}", three_col_bold_format_data)
            # 待补货
            self._apply_cell_format(sheet_id, f"A{start_row + 3}:A{start_row + 3}", single_bold_format_data)
            # 待处理
            self._apply_cell_format(sheet_id, f"A{start_row + 4}:A{start_row + 4}", single_bold_format_data)
            # 生产单未完成
            self._apply_cell_format(sheet_id, f"A{start_row + 5}:A{start_row + 5}", single_bold_format_data)
            # 已审核
            self._apply_cell_format(sheet_id, f"A{start_row + 6}:A{start_row + 6}", single_bold_format_data)
            # 发货数据
            self._apply_cell_format(sheet_id, f"A{start_row + 7}:A{start_row + 7}", single_bold_format_data)
            # 发货总金额
            self._apply_cell_format(sheet_id, f"A{start_row + 8}:A{start_row + 8}", single_bold_format_data)

            if success1:
                print(f"✅ 成功设置报告块格式")
                return True
            else:
                print(f"⚠️ 格式设置失败，但数据已成功写入")
                return True  # 即使格式设置失败，也返回True，因为数据写入成功

        except Exception as e:
            print(f"⚠️ 设置格式异常: {str(e)}")
            return True  # 即使格式设置失败，也返回True，因为数据写入成功

    def _apply_cell_format(self, sheet_id: str, cell_range: str, format_data: Dict) -> bool:
        """
        应用单元格格式

        Args:
            sheet_id: 工作表ID
            cell_range: 单元格范围
            format_data: 格式数据

        Returns:
            是否成功
        """
        try:
            # 使用正确的钉钉API端点
            url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{cell_range}?operatorId={self.operator_id}"

            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'x-acs-dingtalk-access-token': self.access_token
            }

            print(f"🔍 格式设置调试:")
            print(f"  URL: {url}")
            print(f"  格式数据: {format_data}")

            response = requests.put(url, json=format_data, headers=headers, verify=False)

            print(f"  响应状态码: {response.status_code}")
            print(f"  响应内容: {response.text}")

            if response.status_code == 200:
                return True
            else:
                print(f"⚠️ 设置格式失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"⚠️ 应用格式异常: {str(e)}")
            return False

    def _merge_cells_for_report_block(self, sheet_id: str, start_row: int, end_row: int, status_data: Dict, shipping_data: Dict, current_time: str, reward_data: Dict = None) -> bool:
        """
        为报告块合并单元格

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功
        """
        try:
            # 按照用户要求合并指定行的B:C列，并设置对应的值
            # 使用传入的时间参数，确保时间格式一致

            # 提取数据
            waiting_stock = status_data.get('待补货', {})
            waiting_process = status_data.get('待处理', {})
            unfinished_production = status_data.get('生产单未完成单数', {})
            total_production = status_data.get('总生产单数', {})
            audited_custom = status_data.get('已审核-定制', {})
            audited_stock = status_data.get('已审核-现货', {})

            # 计算生产单完成进度
            unfinished_count = unfinished_production.get('总数', 0)
            total_count = total_production.get('总数', 0)
            if total_count > 0:
                finished_count = total_count - unfinished_count
                progress_text = f"未完成：{unfinished_count}/{total_count}，已完成：{finished_count}/{total_count}"
            else:
                progress_text = "暂无生产单数据"

            merge_configs = [
                (f"B{start_row}:C{start_row}", current_time),      # 数据生成时间行
                (f"A{start_row + 1}:C{start_row + 1}", "仓库状态数据"),  # 仓库状态数据行（整行合并）
                (f"B{start_row + 5}:C{start_row + 5}", progress_text),  # 生产单完成进度行
                (f"B{start_row + 6}:C{start_row + 6}", str(audited_custom.get('订单数', 0))),  # 已审核-定制行
                (f"B{start_row + 7}:C{start_row + 7}", str(audited_stock.get('订单数', 0))),  # 已审核-现货行
                (f"A{start_row + 8}:C{start_row + 8}", "发货数据"),  # 发货数据标题行（整行合并）
                (f"B{start_row + 9}:C{start_row + 9}", str(shipping_data.get('现货订单数', 0))),  # 现货订单数行
                (f"B{start_row + 10}:C{start_row + 10}", f"{shipping_data.get('现货订单发货总重量', 0.0)}kg"),  # 现货订单发货总重量行
                (f"B{start_row + 11}:C{start_row + 11}", str(shipping_data.get('定制订单数', 0))),  # 定制订单数行
                (f"B{start_row + 12}:C{start_row + 12}", f"{shipping_data.get('定制订单发货总重量', 0.0)}kg"),  # 定制订单发货总重量行
            ]

            # 如果有奖惩数据，添加奖惩相关的合并配置
            if reward_data:
                # 构造奖惩结果详情文本（包含计算规则）
                reward_detail = (f"已审核订单：{reward_data.get('audited_orders', 0):,}单 "
                               f"待补货订单：{reward_data.get('waiting_stock_orders', 0)}单 "
                               f"待处理订单：{reward_data.get('waiting_process_orders', 0)}单 | "
                               f"待补货奖惩（10元规则）：{reward_data.get('stock_condition', '')} "
                               f"结果：{reward_data.get('stock_result', '')} {abs(reward_data.get('stock_reward', 0))}元 | "
                               f"待处理奖惩（25元规则）：{reward_data.get('process_condition', '')} "
                               f"结果：{reward_data.get('process_result', '')} {abs(reward_data.get('process_reward', 0))}元 | "
                               f"总计奖惩：{reward_data.get('total_reward', 0):+d}元")

                reward_configs = [
                    (f"A{start_row + 13}:C{start_row + 13}", "奖惩计算"),  # 奖惩计算标题行（整行合并）
                    (f"B{start_row + 14}:C{start_row + 14}", f"{reward_data.get('stock_result', '')} {reward_data.get('stock_reward', 0):+d}元"),  # 待补货奖惩行
                    (f"B{start_row + 15}:C{start_row + 15}", f"{reward_data.get('process_result', '')} {reward_data.get('process_reward', 0):+d}元"),  # 待处理奖惩行
                    (f"B{start_row + 16}:C{start_row + 16}", f"{reward_data.get('total_reward', 0):+d}元"),  # 总计奖惩行
                    (f"A{start_row + 17}:C{start_row + 17}", reward_detail),  # 奖惩计算结果详情行（整行合并）
                ]
                merge_configs.extend(reward_configs)

            for merge_range, value in merge_configs:
                success = self._merge_cells(sheet_id, merge_range, value)
                if not success:
                    print(f"⚠️ 合并单元格失败: {merge_range}")

            # 注意：钉钉在线表格API不支持行高设置功能
            # 如果有奖惩数据，原本计划为奖惩计算结果行设置35像素行高，但API不支持此功能

            return True

        except Exception as e:
            print(f"⚠️ 合并单元格异常: {str(e)}")
            return False



    def find_last_data_row(self, sheet_id: str, column_to_check: str = 'A', max_check_rows: int = 1000) -> int:
        """
        高效地查找指定列中最后包含数据的行。
        通过一次性读取大块数据并在本地处理来优化性能。

        Args:
            sheet_id: 工作表ID。
            column_to_check: 用于检查的列，默认为'A'。
            max_check_rows: 一次性检查的最大行数。

        Returns:
            最后包含数据的行号，如果工作表为空则返回0。
        """
        print(f"⚡️ 正在高效查找最后一行数据（一次性检查 {max_check_rows} 行）...")
        
        range_to_check = f"{column_to_check}1:{column_to_check}{max_check_rows}"
        
        try:
            # 一次性获取大范围数据
            cell_values = self.get_cell_range(sheet_id, range_to_check)
            
            if not cell_values:
                print("ℹ️ 工作表为空。")
                return 0

            # 从后往前遍历以快速找到最后一行
            for i in range(len(cell_values) - 1, -1, -1):
                row_data = cell_values[i]
                # 检查单元格是否有值
                if row_data and row_data[0] is not None and str(row_data[0]).strip() != '':
                    last_row = i + 1
                    print(f"✅ 找到最后一行数据在: {last_row}")
                    return last_row
            
            print("ℹ️ 在检查范围内未找到任何数据，可能为空表。")
            return 0

        except Exception as e:
            print(f"❌ 查找最后一行时出错: {str(e)}")
            return 0  # 发生错误时返回0，以避免后续逻辑出错

    def _check_and_correct_last_row(self, sheet_id: str, recorded_last_row: int) -> int:
        """
        检查并校正最后一行记录

        检查记录的最后一行往上15行范围内是否有空白行，如果有则触发全表行号检查和校验纠正

        Args:
            sheet_id: 工作表ID
            recorded_last_row: 配置文件中记录的最后一行

        Returns:
            校正后的最后一行号
        """
        try:
            if recorded_last_row <= 15:
                # 如果记录的行号太小，直接返回
                return recorded_last_row

            # 检查往上15行范围内是否有空白行
            check_start_row = max(1, recorded_last_row - 15)
            check_end_row = recorded_last_row - 1

            print(f"🔍 检查第 {check_start_row}-{check_end_row} 行范围内是否有空白行...")

            empty_rows_found = []

            # 检查这个范围内的每一行
            for check_row in range(check_start_row, check_end_row + 1):
                try:
                    check_range = f"A{check_row}:C{check_row}"
                    cell_values = self.get_cell_range(sheet_id, check_range)

                    # 检查是否为空白行（所有单元格都为空或只包含空字符串）
                    is_empty_row = True
                    if cell_values and len(cell_values) > 0:
                        for row in cell_values:
                            for cell in row:
                                if cell and str(cell).strip():
                                    is_empty_row = False
                                    break
                            if not is_empty_row:
                                break

                    if is_empty_row:
                        empty_rows_found.append(check_row)

                except Exception as e:
                    # 单行检查失败，跳过这一行
                    continue

            if empty_rows_found:
                print(f"⚠️ 检测到空白行: {empty_rows_found}，触发全表行号检查...")

                # 触发全表检查，找到真正的最后一行
                actual_last_row = self.find_last_data_row(sheet_id)

                if actual_last_row != recorded_last_row:
                    print(f"📝 行号校正：记录的最后一行 {recorded_last_row} -> 实际最后一行 {actual_last_row}")
                    return actual_last_row
                else:
                    print(f"✅ 行号检查通过，最后一行确实是 {recorded_last_row}")
                    return recorded_last_row
            else:
                print(f"✅ 第 {check_start_row}-{check_end_row} 行范围内无空白行，行号记录正常")
                return recorded_last_row

        except Exception as e:
            print(f"⚠️ 行号检查异常: {str(e)}，使用原记录行号 {recorded_last_row}")
            return recorded_last_row

    def _merge_cells(self, sheet_id: str, cell_range: str, value: str = "") -> bool:
        """
        合并指定范围的单元格并设置值和格式

        Args:
            sheet_id: 工作表ID
            cell_range: 要合并的单元格范围，如 "B1:C1"
            value: 要在合并后的单元格中显示的值

        Returns:
            是否成功
        """
        try:
            # 先合并单元格
            merge_url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{cell_range}/merge?operatorId={self.operator_id}"

            headers = {
                'Content-Type': 'application/json',
                'x-acs-dingtalk-access-token': self.access_token
            }

            # 使用POST方法合并
            merge_response = requests.post(merge_url, headers=headers, verify=False)

            if merge_response.status_code == 200:
                print(f"✅ 成功合并单元格: {cell_range}")

                # 如果有值，则设置合并后单元格的值和格式
                if value:
                    # 获取合并后的起始单元格（如B1:C1合并后是B1）
                    start_cell = cell_range.split(':')[0]

                    # 设置值和居中格式 - 使用正确的钉钉API参数名称，并添加加粗
                    format_data = {
                        "values": [[value]],
                        "horizontalAlignments": [["center"]],
                        "verticalAlignments": [["middle"]],
                        "fontFamilies": [["SimHei"]],  # 黑体
                        "fontSizes": [[10]],           # 10号字体
                        "fontBolds": [[True]]          # 加粗字体
                    }

                    # 如果是时间格式，强制设置为文本格式以避免转换为数字
                    if "/" in value and ":" in value:  # 检测时间格式
                        # 使用文本格式而不是日期格式，避免Excel自动转换
                        format_data["numberFormats"] = [["@"]]  # @ 表示文本格式
                        print(f"🕐 检测到时间格式，设置为文本格式: {value}")

                    format_url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{start_cell}?operatorId={self.operator_id}"
                    format_response = requests.put(format_url, json=format_data, headers=headers, verify=False)

                    if format_response.status_code == 200:
                        print(f"✅ 成功设置合并单元格的值和格式: {start_cell} = '{value}'")
                    else:
                        print(f"⚠️ 设置合并单元格格式失败: {format_response.status_code} - {format_response.text}")

                return True
            else:
                print(f"⚠️ 合并单元格失败: {merge_response.status_code} - {merge_response.text}")
                return False

        except Exception as e:
            print(f"⚠️ 合并单元格异常: {str(e)}")
            return False

    def _add_borders_to_report_block(self, sheet_id: str, start_row: int, end_row: int) -> bool:
        """
        为报告块添加边框
        注意：钉钉表格API不支持边框设置，此方法仅作为占位符

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功（总是返回True，因为边框功能不可用）
        """
        try:
            range_notation = f"A{start_row}:C{end_row}"
            print(f"ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: {range_notation}")
            return True

        except Exception as e:
            print(f"⚠️ 边框方法异常: {str(e)}")
            return True  # 即使异常也返回True，因为边框不是必需功能



    def _set_comprehensive_format_for_report_block(self, sheet_id: str, start_row: int, end_row: int) -> bool:
        """
        为报告块设置综合格式（居中、字体、字号等）

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功
        """
        try:
            # 设置整个报告块的综合格式
            range_notation = f"A{start_row}:C{end_row}"

            # 计算行数和列数
            rows = end_row - start_row + 1
            cols = 3

            # 创建格式数组 - 使用正确的钉钉API参数名称，并添加加粗功能
            format_data = {
                "horizontalAlignments": [["center"] * cols for _ in range(rows)],  # 水平居中
                "verticalAlignments": [["middle"] * cols for _ in range(rows)],    # 垂直居中
                "fontFamilies": [["SimHei"] * cols for _ in range(rows)],          # 黑体
                "fontSizes": [[10] * cols for _ in range(rows)],                   # 10号字体
                "fontBolds": [[True] * cols for _ in range(rows)]                  # 加粗字体
            }

            url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{range_notation}?operatorId={self.operator_id}"

            headers = {
                'Content-Type': 'application/json',
                'x-acs-dingtalk-access-token': self.access_token
            }

            response = requests.put(url, json=format_data, headers=headers, verify=False)

            if response.status_code == 200:
                print(f"✅ 成功设置综合格式: {range_notation}")
                return True
            else:
                print(f"⚠️ 设置综合格式失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"⚠️ 设置综合格式异常: {str(e)}")
            return False





    def _get_next_available_row(self, sheet_id: str, last_row_hint: int = None) -> int:
        """
        获取下一个可用的行号（用于追加报告块）
        会自动在新数据前留出一个空行作为分隔

        Args:
            sheet_id: 工作表ID
            last_row_hint: 上次记录的最后一行号（可选，用于优化性能）

        Returns:
            下一行的行号，失败返回None
        """
        try:
            # 如果有上次记录的最后一行提示，直接使用（已在写入前完成检测和校正）
            if last_row_hint and last_row_hint > 0:
                print(f"📍 使用配置文件中记录的最后一行: {last_row_hint}")
                last_data_row = last_row_hint
            else:
                # 读取A列的数据来确定下一行
                # 从第1行开始检查，找到连续的空行区域
                last_data_row = 0

                # 先找到最后一行有数据的行号，限制检查范围避免无限循环
                print("🔍 正在查找最后一行数据...")
                for row in range(1, 21):  # 最多检查20行，避免无限循环
                    try:
                        cell_data = self.get_cell_range(sheet_id, f"A{row}:C{row}")
                        if cell_data and cell_data[0] and any(cell for cell in cell_data[0] if cell and cell.strip()):
                            last_data_row = row
                            print(f"  - 第 {row} 行有数据")
                    except Exception as e:
                        print(f"  - 检查第 {row} 行时出错: {str(e)}")
                        # 如果单行检查失败，跳过这一行继续
                        continue

            # 如果是第一次写入数据（没有找到任何数据）
            if last_data_row == 0:
                print("📍 表格为空，将从第1行开始写入")
                return 1

            # 如果已有数据，在最后一行数据后留出一个空行，然后返回下一行
            next_row = last_data_row + 2  # +1是紧接着的行，+2是留出一个空行

            print(f"📍 找到最后数据行: {last_data_row}，下一个数据块将从第 {next_row} 行开始（留出1行空行分隔）")
            return next_row

        except Exception as e:
            print(f"❌ 获取下一行位置失败: {str(e)}")
            # 如果完全失败，返回一个默认位置
            print("⚠️ 使用默认位置第1行")
            return 1

    def merge_cells(self, sheet_id: str, cell_range: str) -> bool:
        """
        合并单元格

        Args:
            sheet_id: 工作表ID
            cell_range: 单元格范围，如 "A1:C1"

        Returns:
            是否成功
        """
        try:
            url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{cell_range}/merge"
            headers = self.default_headers.copy()
            headers['operatorId'] = self.operator_id

            response = requests.post(url, headers=headers, verify=False, timeout=10)

            if response.status_code == 200:
                print(f"✅ 成功合并单元格: {cell_range}")
                return True
            else:
                print(f"❌ 合并单元格失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 合并单元格异常: {str(e)}")
            return False

    def set_cell_alignment(self, sheet_id: str, cell_range: str, alignment: str = "center", values: List[List[Any]] = None) -> bool:
        """
        设置单元格对齐方式（支持同时设置内容和对齐方式）

        Args:
            sheet_id: 工作表ID
            cell_range: 单元格范围，如 "A1:C1"
            alignment: 对齐方式, "left", "center", "right"
            values: 可选，要写入的数据，二维列表格式。如果不提供，只设置对齐方式

        Returns:
            是否成功
        """
        try:
            # 使用与write_cell_range相同的URL格式
            url = (f"{self.primary_domain}{self.sheet_id}"
                   f"/sheets/{sheet_id}/ranges/{cell_range}"
                   f"?operatorId={self.operator_id}")

            # 构建请求数据，根据钉钉API要求使用二维数组格式
            data = {
                "horizontalAlignment": alignment.upper()
            }

            # 如果提供了values，添加到请求数据中
            if values is not None:
                data["values"] = values
                print(f"🔍 设置单元格内容和对齐方式:")
                print(f"  范围: {cell_range}")
                print(f"  内容: {values}")
                print(f"  对齐: {alignment}")
            else:
                print(f"🔍 仅设置单元格对齐方式:")
                print(f"  范围: {cell_range}")
                print(f"  对齐: {alignment}")

            response = requests.put(url, data=json.dumps(data), headers=self.default_headers, verify=False, timeout=10)

            print(f"  响应状态码: {response.status_code}")
            print(f"  响应内容: {response.text}")

            if response.status_code == 200:
                if values is not None:
                    print(f"✅ 成功设置单元格内容和对齐方式: {cell_range} -> {alignment}")
                else:
                    print(f"✅ 成功设置单元格对齐方式: {cell_range} -> {alignment}")
                return True
            else:
                print(f"❌ 设置单元格对齐方式失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 设置单元格对齐方式异常: {str(e)}")
            return False

    def write_monthly_summary_title(self, sheet_id: str, cell_range: str, title: str) -> bool:
        """
        写入月份汇总标题并设置居中对齐

        Args:
            sheet_id: 工作表ID
            cell_range: 单元格范围，如 "A1:E1"
            title: 标题内容，如 "2025年08月数据汇总"

        Returns:
            是否成功
        """
        try:
            # 将标题内容格式化为二维数组
            title_values = [[title]]

            print(f"📝 写入月份汇总标题:")
            print(f"  标题: {title}")
            print(f"  范围: {cell_range}")
            print(f"  格式: 居中对齐")

            # 使用修改后的set_cell_alignment方法同时设置内容和对齐方式
            return self.set_cell_alignment(sheet_id, cell_range, "center", title_values)

        except Exception as e:
            print(f"❌ 写入月份汇总标题异常: {str(e)}")
            return False
