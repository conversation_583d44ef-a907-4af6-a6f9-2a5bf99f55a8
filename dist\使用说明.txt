# 售后绩效数据同步程序

## 程序说明
本程序用于从Excel文件读取售后部门绩效数据，并自动同步到钉钉在线表格。

## 文件说明
- `售后绩效数据同步.exe` - 主程序文件
- `运行程序.bat` - 快速运行批处理文件
- `config.json` - 配置文件（包含钉钉API配置和Excel文件路径）

## 使用方法

### 方法一：使用批处理文件（推荐）
双击 `运行程序.bat` 文件，程序会自动处理昨天的数据。

### 方法二：命令行运行
打开命令提示符，切换到程序目录，然后运行：

```bash
# 处理昨天的数据（默认）
售后绩效数据同步.exe

# 处理指定日期的数据
售后绩效数据同步.exe --date 2025-07-20

# 查看帮助信息
售后绩效数据同步.exe --help
```

## 配置说明
程序运行前请确保：
1. `config.json` 文件中的Excel文件路径正确
2. 钉钉API配置信息正确
3. Excel文件存在且包含所需的数据列

## 注意事项
1. 程序会自动检查钉钉表格中是否已存在相同日期的数据
2. 如果存在则更新，不存在则新增
3. 如果Excel中没有找到指定日期的数据，会写入"暂无数据"
4. 程序按月份自动创建数据汇总区域

## 故障排除
如果程序运行出错，请检查：
1. Excel文件路径是否正确
2. Excel文件是否被其他程序占用
3. 网络连接是否正常
4. 钉钉API配置是否正确

## 技术支持
如有问题请联系开发人员。
