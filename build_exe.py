#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
售后绩效数据同步程序打包脚本
使用PyInstaller将Python程序打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("🔧 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['售后绩效数据同步.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('dingtalk_sheet_utils.py', '.'),
    ],
    hiddenimports=[
        'pandas._libs.tslibs.timedeltas',
        'pandas._libs.tslibs.np_datetime',
        'pandas._libs.tslibs.nattype',
        'pandas._libs.properties',
        'openpyxl.cell._writer',
        'openpyxl.workbook.external_link.external',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不必要的大型库和模块
        'matplotlib',
        'matplotlib.pyplot',
        'seaborn',
        'plotly',
        'bokeh',
        'scipy',
        'numpy.distutils',
        'numpy.f2py',
        'numpy.testing',
        'pandas.plotting',
        'pandas.tests',
        'pandas.io.formats.style',
        'pandas.io.clipboard',
        'pandas.io.html',
        'pandas.io.sql',
        'pandas.io.sas',
        'pandas.io.spss',
        'pandas.io.stata',
        'pandas.io.gbq',
        'pandas.io.parquet',
        'pandas.io.feather',
        'pandas.io.orc',
        'pandas.io.xml',
        'pandas.plotting._matplotlib',
        'IPython',
        'jupyter',
        'notebook',
        'qtconsole',
        'spyder',
        'pytest',
        'unittest',
        'doctest',
        'pdb',
        'cProfile',
        'profile',
        'pstats',
        'timeit',
        'trace',
        'tkinter',
        'turtle',
        'curses',
        'multiprocessing.dummy',
        'concurrent.futures',
        'asyncio',
        'email',
        'html',
        'http.server',
        'xmlrpc',
        'wsgiref',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyd = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyd,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='售后绩效数据同步',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[
        'vcruntime140.dll',
        'msvcp140.dll',
        'api-ms-win-*.dll'
    ],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    optimize=2,
)
'''
    
    with open('售后绩效数据同步.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建规格文件成功")

def build_exe():
    """执行打包"""
    print("🚀 开始打包程序...")
    
    # 检查必要文件是否存在
    required_files = [
        '售后绩效数据同步.py',
        'dingtalk_sheet_utils.py',
        'config.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    try:
        # 使用规格文件打包
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "售后绩效数据同步.spec"]
        subprocess.check_call(cmd)
        print("✅ 程序打包成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False

def create_batch_file():
    """创建批处理文件"""
    batch_content = '''@echo off
chcp 65001 > nul
echo 售后绩效数据同步程序
echo ========================
echo.
echo 使用方法:
echo 1. 处理昨天的数据（默认）: 直接运行
echo 2. 处理指定日期的数据: 售后绩效数据同步.exe --date 2025-07-20
echo.
echo 按任意键开始执行默认任务（处理昨天的数据）...
pause > nul
echo.
echo 正在执行...
售后绩效数据同步.exe
echo.
echo 任务完成，按任意键退出...
pause > nul
'''
    
    with open('dist/运行程序.bat', 'w', encoding='gbk') as f:
        f.write(batch_content)
    print("✅ 创建批处理文件成功")

def create_readme():
    """创建说明文件"""
    readme_content = '''# 售后绩效数据同步程序

## 程序说明
本程序用于从Excel文件读取售后部门绩效数据，并自动同步到钉钉在线表格。

## 文件说明
- `售后绩效数据同步.exe` - 主程序文件
- `运行程序.bat` - 快速运行批处理文件
- `config.json` - 配置文件（包含钉钉API配置和Excel文件路径）

## 使用方法

### 方法一：使用批处理文件（推荐）
双击 `运行程序.bat` 文件，程序会自动处理昨天的数据。

### 方法二：命令行运行
打开命令提示符，切换到程序目录，然后运行：

```bash
# 处理昨天的数据（默认）
售后绩效数据同步.exe

# 处理指定日期的数据
售后绩效数据同步.exe --date 2025-07-20

# 查看帮助信息
售后绩效数据同步.exe --help
```

## 配置说明
程序运行前请确保：
1. `config.json` 文件中的Excel文件路径正确
2. 钉钉API配置信息正确
3. Excel文件存在且包含所需的数据列

## 注意事项
1. 程序会自动检查钉钉表格中是否已存在相同日期的数据
2. 如果存在则更新，不存在则新增
3. 如果Excel中没有找到指定日期的数据，会写入"暂无数据"
4. 程序按月份自动创建数据汇总区域

## 故障排除
如果程序运行出错，请检查：
1. Excel文件路径是否正确
2. Excel文件是否被其他程序占用
3. 网络连接是否正常
4. 钉钉API配置是否正确

## 技术支持
如有问题请联系开发人员。
'''
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 创建说明文件成功")

def copy_config_file():
    """复制配置文件到dist目录"""
    if os.path.exists('config.json'):
        shutil.copy2('config.json', 'dist/')
        print("✅ 复制配置文件成功")
    else:
        print("⚠️ 配置文件不存在，请手动复制")

def clean_build_files():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    # 删除构建目录
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 删除规格文件
    if os.path.exists('售后绩效数据同步.spec'):
        os.remove('售后绩效数据同步.spec')
    
    print("✅ 清理完成")

def main():
    """主函数"""
    print("🚀 售后绩效数据同步程序打包工具")
    print("=" * 50)
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ 无法安装PyInstaller，请手动安装")
            return
    
    # 创建规格文件
    create_spec_file()
    
    # 执行打包
    if build_exe():
        # 复制配置文件
        copy_config_file()
        
        # 创建批处理文件
        create_batch_file()
        
        # 创建说明文件
        create_readme()
        
        # 清理构建文件
        clean_build_files()
        
        print("\n🎉 打包完成！")
        print("📁 可执行文件位置: dist/售后绩效数据同步.exe")
        print("📋 使用说明: dist/使用说明.txt")
        print("🚀 快速运行: dist/运行程序.bat")
    else:
        print("\n❌ 打包失败")

if __name__ == '__main__':
    main()
